
这个改造方案充分考虑了Aspose.Words和Aspose.Slides的差异，保留了通用的文件处理功能，删除了Word特有的功能，并添加了PPT演示文稿特有的功能。整个改造过程将分阶段进行，确保每个阶段都能正常运行和测试。
您希望我开始实施哪个阶段的改造工作？我建议从第一阶段的基础架构调整开始。





Aspose.Slides对于页面设置、内容删除设置、内容替换设置、段落格式设置、页眉页脚设置、文档属性、文件名替换、ppt转pdf  九个大类，分别支持哪些功能？按分类详细给我列出。


我来详细分析Aspose.Slides在这九个大类中支持的具体功能。让我先查看相关的API文档信息：

让我继续查看更多关于演示文稿设置、文档属性等功能的信息：



在当前目录作为项目根目录，写一个Visual Studio 2022项目的视窗exe软件用于使用Aspose.Slides功能对ppt批量处理，aspose版本为25.4.0.0，功能实现参考Aspose.Slides官方api文档，aspose使用项目目录里的版本，许可证在项目里Aspose.Total.NET.lic。.net版本要求为6.0.相关功能要求如下
软件布局：软件由上而下分为若干区域
一、路径区域：
来源目录（可选是否处理子目录）及浏览按钮
目标区域（处理后的文件到达复制到的位置，可选是否保持原目录结构）及浏览按钮
移动文件时冲突处理选项。是否处理源文件勾选项、复制移动文件单选项。线程数设定输入框。重试次数设定输入框。
支持格式（弹窗设置，勾选的文档才加入处理流程）。批处理数量输入框。全选、取消全选按钮（用于对下面的功能区域勾选框进行批量勾选、批量取消勾选）
二、功能区域，九个功能按钮分为3*3排列，每个功能前有个单独启用框。每个功能弹窗设置详细内容，弹窗内每类子功能单独一个区域，如果子功能分类多就分多个标签页，每个子功能有单独的启用开关，每个标签页要有总开关。

（一）、 页面设置
注意：PPT与Word的页面概念不同，PPT使用"幻灯片尺寸"概念

1、幻灯片尺寸设置
SlideSize.OnScreen16x9 (16:9宽屏)
SlideSize.OnScreen4x3 (4:3标准)
SlideSize.A3, SlideSize.A4 (纸张尺寸)
SlideSize.B4, SlideSize.B5 (纸张尺寸)
SlideSize.Letter, SlideSize.Ledger (美式纸张)
自定义尺寸设置
2、幻灯片方向
横向/纵向切换
尺寸比例调整

（二）、内容删除设置
1、幻灯片删除
删除空白幻灯片
删除指定幻灯片
批量删除幻灯片
2、形状内容删除
删除文本框
删除图片
删除表格
删除图表
删除音频/视频
3、动画删除
删除所有动画效果
删除指定动画
删除切换效果
4、备注删除
删除幻灯片备注
清空备注内容

（三）、 内容替换设置
1、文本替换
普通文本替换
正则表达式替换
批量文本替换
指定范围替换（标题、内容、备注）
2、形状替换
图片替换
文本框替换
形状样式替换
3、字体替换
字体名称替换
字体样式替换
字体嵌入
4、颜色替换
主题颜色替换
自定义颜色替换

（四）、PPT格式设置
1、主题设置
应用内置主题
自定义主题
主题颜色方案
主题字体方案
2、母版设置
幻灯片母版
标题母版
备注母版
讲义母版
3、布局设置
标题幻灯片布局
内容布局
两栏布局
图片布局
自定义布局
4、样式设置
形状样式
文本样式
表格样式
图表样式

（五）、 页眉页脚设置
1、幻灯片页脚
页脚文本设置
日期时间显示
幻灯片编号
页脚位置调整
2、备注和讲义页眉页脚
页眉文本
页脚文本
日期时间
页码


（六）、文档属性
1、内置属性
标题 (Title)
作者 (Author)
主题 (Subject)
关键词 (Keywords)
描述 (Comments)
类别 (Category)
公司 (Company)
管理者 (Manager)
2、时间属性
创建时间
修改时间
最后打印时间
总编辑时间
3、统计属性
幻灯片数量
段落数量
字数统计
字符数统计
4、自定义属性
自定义属性名称
自定义属性值
属性类型（文本、数字、日期、布尔）


（七）、 文件名替换

1、文件名模式替换
正则表达式匹配
通配符匹配
批量重命名规则
文件名生成
2、基于文档属性生成
基于内容生成
序号生成
日期时间生成
3、冲突处理
沿用软件主面板的冲突处理方式

（八）、PPT转PDF
1、基本转换
PPT/PPTX转PDF
批量转换
保持原始格式
2、转换选项
页面范围选择
图片质量设置
压缩选项
密码保护
3、布局选项
幻灯片布局
讲义布局
备注布局
大纲布局
4、高级选项
嵌入字体
图片压缩
元数据保留
书签生成

（九）、 其他格式转换
1、图片格式
PPT转PNG
PPT转JPEG
PPT转BMP
PPT转TIFF
PPT转SVG
2、Web格式
PPT转HTML
PPT转XAML
3、其他格式
PPT转XPS
PPT转SWF
幻灯片缩略图生成

这个分类详细列出了Aspose.Slides在您关注的九个大类中的具体功能支持情况，可以看出它在PPT处理方面提供了非常全面的功能支持。